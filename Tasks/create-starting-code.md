Please create and configure the Firebase Functions necessary for a secure SMS OTP and user login system. This system should include two HTTP-triggered functions: sendOtp and verifyOtpAndSignupLogin.

The services to be used:
- Firebase ("towasl" project id)
- Firestore (Database)
- Firebase Functions (Backend)
- Msegat (SMS provider for OTP) https://msegat.docs.apiary.io/
- Firebase Auth (Sessions Management)

The flow:
Client ──▶ /sendOtp ──▶ Firebase Function ──▶ Msegat API       ◀─────────────────────────────── Send OTP to mobileClient ──▶ /verifyOtpAndSignupLogin ──▶ Firebase Function ──▶ Verify OTP                                            └▶ createCustomToken()
                                            └▶userSignupLogin() to check if user exist in Firestore then createCustomToken() or create the user then createCustomToken       ◀─────────────────────────────── Return customTokenClient ──▶ FirebaseAuth.signInWithCustomToken(customToken)           ──▶ Firestore secured via request.auth.uid

The sendOtp function should generate a 4-digit OTP, store it in Firestore (in a collection named otp_requests with the phone number as the document ID, including otp, createdAt, and expiryTime fields), and then send the OTP via the Msegat SMS API.
The verifyOtpAndSignupLogin function should verify the OTP against the stored record in Firestore, check for expiry, and upon successful verification, either retrieve an existing Firebase Auth user by phone number or create a new one if not found. It should also create/update a user document in Firestore (in a users collection using the Firebase Auth UID as the document ID). Finally, it must mint and return a custom Firebase Authentication token for the user.
Ensure proper CORS handling for both functions.
Configure Firestore security rules to deny client-side access to the otp_requests collection and allow authenticated users to read/write their own data in a users collection linked to their UID.
Data to Provide to the AI Agent:

Here is the data required for the Firebase Functions:
[FIREBASE_PROJECT_ID]: towasl
[MSEGAT_USERNAME]: 
[MSEGAT_API_KEY]: 
[MSEGAT_SENDER_ID]: auth-mseg
[MSEGAT_OTP_MESSAGE_TEMPLATE]: رمز التحقق: xxxx

Explanation for the AI Agent:

The AI agent will use the Firebase Project ID to ensure the functions are deployed to the correct Firebase project.
The Msegat credentials will be set as Firebase Functions environment variables (e.g., using firebase functions:config:set or secrets) so the functions can securely access them at runtime to send SMS messages.
The Firebase Admin SDK, once initialized (admin.initializeApp()), will automatically connect to your Firebase project's Firestore and Authentication services.