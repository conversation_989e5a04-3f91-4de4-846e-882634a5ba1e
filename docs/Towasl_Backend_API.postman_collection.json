{"info": {"name": "Towasl Backend API", "description": "Firebase Functions API for SMS OTP authentication with separate country code and mobile number handling.\n\n## Setup Instructions:\n1. Set the `baseUrl` variable to your deployed Firebase Functions URL\n2. Set the `apiKey` variable to your API key (get it with: firebase functions:config:get)\n3. For deleteAccount endpoint, you'll need to set the `firebaseIdToken` variable after authentication\n\n## Authentication Flow:\n1. Send OTP → 2. Verify OTP → 3. Use returned customToken with Firebase Auth → 4. Use Firebase ID token for protected endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "towasl-backend-api-collection", "version": {"major": 1, "minor": 0, "patch": 0}}, "variable": [{"key": "baseUrl", "value": "https://us-central1-towasl.cloudfunctions.net", "description": "Base URL for Firebase Functions"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "your-api-key-here", "description": "API Key for authentication (get with: firebase functions:config:get)"}, {"key": "firebaseIdToken", "value": "your-firebase-id-token-here", "description": "Firebase ID Token for protected endpoints (obtained after Firebase Auth)"}, {"key": "testCountryCode", "value": "+966", "description": "Test country code (Saudi Arabia)"}, {"key": "testMobile", "value": "*********", "description": "Test mobile number (replace with real number for actual testing)"}], "item": [{"name": "Authentication Flow", "description": "Complete SMS OTP authentication flow", "item": [{"name": "1. Send OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}", "description": "API Key for authentication"}], "body": {"mode": "raw", "raw": "{\n  \"countryCode\": \"{{testCountryCode}}\",\n  \"mobile\": \"{{testMobile}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/sendOtp", "host": ["{{baseUrl}}"], "path": ["sendOtp"]}, "description": "Send 4-digit <PERSON><PERSON> via SMS to the provided phone number.\n\n**Request Body:**\n- `countryCode`: Country code with + prefix (e.g., \"+966\")\n- `mobile`: Mobile number without country code (e.g., \"*********\")\n\n**Success Response:**\n- `success`: true\n- `message`: \"O<PERSON> sent successfully\"\n- `expiryTime`: Unix timestamp (OTP expires in 5 minutes)\n\n**Rate Limits:**\n- 1 request per minute per phone number\n- 10 requests per hour per IP address"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}"}], "body": {"mode": "raw", "raw": "{\n  \"countryCode\": \"+966\",\n  \"mobile\": \"*********\"\n}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"message\": \"OTP sent successfully\",\n  \"expiryTime\": 1703123456789\n}"}]}, {"name": "2. Verify OTP & Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}", "description": "API Key for authentication"}], "body": {"mode": "raw", "raw": "{\n  \"countryCode\": \"{{testCountryCode}}\",\n  \"mobile\": \"{{testMobile}}\",\n  \"otp\": \"1234\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/verifyOtpAndSignupLogin", "host": ["{{baseUrl}}"], "path": ["verifyOtpAndSignupLogin"]}, "description": "Verify the OTP and handle user signup/login. Creates new user if doesn't exist.\n\n**Request Body:**\n- `countryCode`: Country code with + prefix\n- `mobile`: Mobile number without country code\n- `otp`: 4-digit OTP received via SMS\n- `userData` (optional): Additional user data object\n\n**Success Response:**\n- `success`: true\n- `message`: \"OTP verified successfully\"\n- `customToken`: Firebase custom token for authentication\n- `user`: User object with uid, phoneNumber, countryCode, mobile, duid\n\n**Rate Limits:**\n- 5 verification attempts per OTP\n- OTP expires in 5 minutes"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}"}], "body": {"mode": "raw", "raw": "{\n  \"countryCode\": \"+966\",\n  \"mobile\": \"*********\",\n  \"otp\": \"1234\"\n}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"message\": \"OTP verified successfully\",\n  \"customToken\": \"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...\",\n  \"user\": {\n    \"uid\": \"firebase_user_uid\",\n    \"phoneNumber\": \"+************\",\n    \"countryCode\": \"+966\",\n    \"mobile\": \"*********\",\n    \"duid\": \"A1B2C3\"\n  }\n}"}]}]}, {"name": "Account Management", "description": "User account management endpoints", "item": [{"name": "Delete Account (Soft Delete)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}", "description": "API Key for authentication"}, {"key": "Authorization", "value": "Bearer {{firebaseIdToken}}", "description": "Firebase ID Token (obtained after Firebase Auth with customToken)"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/deleteAccount", "host": ["{{baseUrl}}"], "path": ["deleteAccount"]}, "description": "Soft delete user account by setting deleted_at timestamp and signing out user from all devices.\n\n**Headers Required:**\n- `X-API-Key`: Your API key\n- `Authorization`: Bearer token with Firebase ID token\n\n**Request Body:** None (empty)\n\n**Success Response:**\n- `success`: true\n- `message`: \"Account deleted successfully\"\n- `deleted_at`: Unix timestamp of deletion\n\n**Note:** This is a soft delete. Account will be permanently deleted after 30 days by scheduled function."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}"}, {"key": "Authorization", "value": "Bearer {{firebaseIdToken}}"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"message\": \"Account deleted successfully\",\n  \"deleted_at\": *************\n}"}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Pre-request script for the collection", "// You can add global pre-request logic here"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script for the collection", "// You can add global test logic here"]}}]}