# Security Recommendations for Towasl Backend API

## 🚨 Current Security Status

**Your API endpoints are currently PUBLICLY ACCESSIBLE** - anyone on the internet can call them.

### What This Means:
- ✅ Anyone can send OTP requests to any phone number
- ✅ Anyone can attempt to verify OTPs (but needs actual OTP)
- ✅ No API authentication required
- ⚠️ **Potential for abuse and SMS spam**

## 🛡️ Security Measures Implemented

### 1. Rate Limiting (NEW)
- **Phone-based**: 1 OTP per phone number per minute
- **IP-based**: 10 OTP requests per IP per hour
- **Verification**: 5 attempts per OTP maximum

### 2. Input Validation
- Phone number format validation
- Required field validation
- Type checking for all inputs

### 3. OTP Security
- 5-minute expiration
- Single-use verification
- Automatic cleanup after use

### 4. Database Security
- OTP collection inaccessible from client-side
- Users can only access their own data
- Firestore security rules enforced

## 🔒 Additional Security Recommendations

### 1. API Key Authentication (RECOMMENDED)
Add API key requirement for production:

```typescript
// Add to function headers
const API_KEY = functions.config().api?.key;

// Validate API key
const providedKey = request.headers['x-api-key'];
if (providedKey !== API_KEY) {
  response.status(401).json({ error: "Unauthorized" });
  return;
}
```

### 2. CORS Restriction (RECOMMENDED)
Update CORS to allow only your domains:

```typescript
const corsHandler = cors({
  origin: [
    'https://yourdomain.com',
    'https://app.yourdomain.com'
  ],
  methods: ["POST"],
  allowedHeaders: ["Content-Type", "X-API-Key"],
});
```

### 3. Firebase App Check (HIGHLY RECOMMENDED)
Verify requests come from your legitimate apps:

```bash
# Enable App Check in Firebase Console
# Add to your functions
firebase functions:config:set appcheck.debug_token="your-debug-token"
```

### 4. Geographic Restrictions
Limit to specific countries if needed:

```typescript
// Add country code validation
const ALLOWED_COUNTRIES = ['+966', '+971', '+965']; // Saudi, UAE, Kuwait
if (!ALLOWED_COUNTRIES.includes(countryCode)) {
  response.status(400).json({ error: "Service not available in this region" });
  return;
}
```

## 🚨 Immediate Actions Needed

### 1. Monitor Usage
```bash
# Check Firebase Functions logs
firebase functions:log

# Monitor for unusual patterns
# - High volume from single IPs
# - Requests to many different phone numbers
# - Failed verification attempts
```

### 2. Set Up Alerts
- Configure Firebase monitoring alerts
- Set up cost alerts for SMS usage
- Monitor error rates and response times

### 3. Implement API Keys (Production)
```bash
# Set API key
firebase functions:config:set api.key="your-secure-api-key-here"

# Update client applications to include API key
# Header: X-API-Key: your-secure-api-key-here
```

## 📊 Testing Security

### Safe Testing Methods:
1. **Use your own phone number** for testing
2. **Test rate limiting** with multiple requests
3. **Verify error handling** with invalid inputs
4. **Check logs** for security events

### Testing Commands:
```bash
# Test rate limiting (should fail after first request)
curl -X POST https://us-central1-towasl.cloudfunctions.net/sendOtp \
  -H "Content-Type: application/json" \
  -d '{"countryCode": "+966", "mobile": "YOUR_NUMBER"}'

# Wait 1 minute, then try again (should succeed)
```

## 🔍 Security Monitoring

### Key Metrics to Watch:
- **OTP request volume** per hour/day
- **Failed verification attempts**
- **Requests from single IPs**
- **SMS costs** and usage patterns
- **Error rates** and types

### Log Analysis:
```bash
# Check for suspicious patterns
firebase functions:log --filter="Rate limit exceeded"
firebase functions:log --filter="Invalid OTP"
firebase functions:log --filter="OTP sent successfully"
```

## 🚀 **Immediate Next Steps (Updated Commands)**

1. **Deploy API Key Authentication** (highest priority):
   ```bash
   # Generate and store API key
   API_KEY=$(openssl rand -hex 16)
   echo "🔑 Your API Key: $API_KEY"
   echo "📝 IMPORTANT: Save this key for your Flutter app!"

   # Configure Firebase
   firebase functions:config:set api.key="$API_KEY"
   firebase functions:config:set api.require_key="true"

   # Deploy
   firebase deploy --only functions

   # Save key securely (optional)
   echo "TOWASL_API_KEY=$API_KEY" >> .env.production
   echo ".env.*" >> .gitignore
   ```

2. **Update your Flutter app** to include API key headers (see FLUTTER_API_KEY_SECURITY.md)

3. **Set up basic monitoring** in Firebase Console

4. **Configure Msegat IP whitelisting** (if available in your dashboard)

5. **Implement Firebase App Check** for maximum security

## 🚀 Production Deployment Checklist

Before going live:
- [ ] Implement API key authentication ✅ (commands above)
- [ ] Restrict CORS to your domains
- [ ] Enable Firebase App Check
- [ ] Set up monitoring and alerts
- [ ] Test rate limiting thoroughly
- [ ] Configure geographic restrictions if needed
- [ ] Set up cost monitoring for SMS
- [ ] Document security procedures for your team

## 💡 Best Practices

1. **Never test with random phone numbers** - only use numbers you control
2. **Monitor costs regularly** - SMS charges can add up quickly
3. **Rotate API keys periodically** if implemented
4. **Keep security logs** for audit purposes
5. **Update dependencies regularly** for security patches

## 🆘 Emergency Response

If you detect abuse:
1. **Immediately check Firebase Console** for unusual activity
2. **Review recent logs** for attack patterns
3. **Consider temporarily disabling functions** if severe
4. **Update rate limits** if needed
5. **Report to Msegat** if SMS abuse detected

---

**Remember**: Your current setup is functional but publicly accessible. Implement additional security measures before production use with real users.
