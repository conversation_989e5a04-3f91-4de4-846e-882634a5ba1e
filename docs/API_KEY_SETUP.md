# API Key Authentication Setup Guide

## 🔑 Step 1: Generate and Configure API Key

### Method 1: Generate and Store API Key (Recommended)

```bash
# 1. Generate API key and store it in a variable
API_KEY=$(openssl rand -hex 16)

# 2. Display the key so you can copy it for Flutter
echo "🔑 Your API Key: $API_KEY"
echo "📝 IMPORTANT: Save this key securely - you'll need it for your Flutter app!"

# 3. Set Firebase configuration
firebase functions:config:set api.key="$API_KEY"
firebase functions:config:set api.require_key="true"

# 4. Optional: Set allowed CORS origins (comma-separated)
firebase functions:config:set cors.allowed_origins="https://yourdomain.com,https://app.yourdomain.com"

# 5. Verify configuration
echo "🔍 Verifying configuration:"
firebase functions:config:get

# 6. Save key to secure file (optional but recommended)
echo "TOWASL_API_KEY=$API_KEY" >> .env.production
echo ".env.*" >> .gitignore
```

### Method 2: Manual Generation

```bash
# 1. Generate and display key
echo "🔑 Generated API Key:"
openssl rand -hex 16

# 2. Copy the output and set it manually
# Replace YOUR_GENERATED_KEY with the actual key from step 1
firebase functions:config:set api.key="YOUR_GENERATED_KEY"
firebase functions:config:set api.require_key="true"

# 3. Verify configuration
firebase functions:config:get
```

### ⚠️ Important Notes:
- **Save the generated key immediately** - you won't be able to retrieve it later
- **The key is 32 characters long** (16 bytes in hexadecimal)
- **Keep it secure** - treat it like a password

### Example Configuration:
```json
{
  "api": {
    "key": "****************************************",
    "require_key": "true"
  },
  "cors": {
    "allowed_origins": "https://yourdomain.com,https://app.yourdomain.com"
  },
  "msegat": {
    "username": "your_msegat_username",
    "api_key": "your_msegat_api_key",
    "sender_id": "auth-mseg",
    "message_template": "رمز التحقق: xxxx"
  }
}
```

## 🚀 Step 2: Deploy Updated Functions

```bash
# Build and deploy
cd functions
npm run build
firebase deploy --only functions
```

## 📱 Step 3: Update Flutter App

### Add API Key to Your Flutter HTTP Requests:

```dart
// services/auth_service.dart
class AuthService {
  static const String baseUrl = 'https://us-central1-towasl.cloudfunctions.net';
  static const String apiKey = '****************************************'; // Your API key
  
  // Send OTP with API key
  Future<bool> sendOtp({
    required String countryCode,
    required String mobile,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/sendOtp'),
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey, // Add API key header
        },
        body: jsonEncode({
          'countryCode': countryCode,
          'mobile': mobile,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized - Invalid API key');
      } else {
        throw Exception('Failed to send OTP: ${response.body}');
      }
    } catch (e) {
      print('Error sending OTP: $e');
      return false;
    }
  }

  // Verify OTP with API key
  Future<Map<String, dynamic>?> verifyOtp({
    required String countryCode,
    required String mobile,
    required String otp,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/verifyOtpAndSignupLogin'),
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey, // Add API key header
        },
        body: jsonEncode({
          'countryCode': countryCode,
          'mobile': mobile,
          'otp': otp,
          'userData': {
            'deviceInfo': await _getDeviceInfo(),
            'appVersion': await _getAppVersion(),
          },
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return data;
        }
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized - Invalid API key');
      }
      
      return null;
    } catch (e) {
      print('Error verifying OTP: $e');
      return null;
    }
  }
}
```

### Secure API Key Storage in Flutter:

```dart
// For production, store API key securely
// Option 1: Environment variables (recommended)
class Config {
  static const String apiKey = String.fromEnvironment('API_KEY', 
    defaultValue: 'your-development-key');
}

// Option 2: Use flutter_dotenv
// pubspec.yaml
dependencies:
  flutter_dotenv: ^5.1.0

// .env file (add to .gitignore)
API_KEY=****************************************

// Load in main.dart
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() async {
  await dotenv.load(fileName: ".env");
  runApp(MyApp());
}

// Use in service
static String get apiKey => dotenv.env['API_KEY'] ?? '';
```

## 🧪 Step 4: Test API Key Authentication

### Test with cURL:

```bash
# Test without API key (should fail with 401)
curl -X POST https://us-central1-towasl.cloudfunctions.net/sendOtp \
  -H "Content-Type: application/json" \
  -d '{"countryCode": "+966", "mobile": "512345678"}'

# Test with API key (should succeed - replace YOUR_GENERATED_KEY with actual key)
curl -X POST https://us-central1-towasl.cloudfunctions.net/sendOtp \
  -H "Content-Type: application/json" \
  -H "X-API-Key: YOUR_GENERATED_KEY" \
  -d '{"countryCode": "+966", "mobile": "YOUR_PHONE_NUMBER"}'

# Alternative: Use the stored variable if still in same terminal session
curl -X POST https://us-central1-towasl.cloudfunctions.net/sendOtp \
  -H "Content-Type: application/json" \
  -H "X-API-Key: $API_KEY" \
  -d '{"countryCode": "+966", "mobile": "YOUR_PHONE_NUMBER"}'
```

### Expected Responses:

**Without API Key (401 Unauthorized):**
```json
{
  "error": "Unauthorized - Invalid or missing API key"
}
```

**With Valid API Key (200 Success):**
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "expiryTime": 1703123456789
}
```

## 🔧 Development vs Production

### Development Mode (API Key Optional):
```bash
firebase functions:config:set api.require_key="false"
```

### Production Mode (API Key Required):
```bash
firebase functions:config:set api.require_key="true"
```

## 🔐 Security Best Practices

1. **Generate Strong API Keys**: Use 32+ character random strings
2. **Rotate Keys Regularly**: Change API keys every 3-6 months
3. **Environment-Specific Keys**: Different keys for dev/staging/production
4. **Never Commit Keys**: Add to .gitignore, use environment variables
5. **Monitor Usage**: Track API key usage in logs

## 🚨 Emergency Key Rotation

If your API key is compromised:

```bash
# 1. Generate new API key
NEW_API_KEY=$(openssl rand -hex 16)
echo "🔑 New API Key: $NEW_API_KEY"

# 2. Set new key in Firebase
firebase functions:config:set api.key="$NEW_API_KEY"

# 3. Deploy immediately
firebase deploy --only functions

# 4. Update all client applications with new key
# 5. Update your secure storage
echo "TOWASL_API_KEY=$NEW_API_KEY" > .env.production

# 6. Monitor logs for unauthorized attempts
firebase functions:log --filter="Invalid API key"
```

## 📊 Monitoring API Key Usage

Check Firebase Functions logs:
```bash
# Monitor unauthorized attempts
firebase functions:log --filter="Unauthorized"

# Monitor successful authentications
firebase functions:log --filter="OTP sent successfully"
```
