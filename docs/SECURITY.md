# Security Guidelines

## Environment Variables & Credentials

### ✅ Secure Practices Implemented

1. **No Hardcoded Credentials**: All sensitive credentials are loaded from Firebase Functions environment configuration
2. **Environment Validation**: Functions validate that required credentials are available before processing requests
3. **Secure Configuration**: Credentials are set using `firebase functions:config:set` command
4. **Version Control Safety**: No actual credentials are committed to the repository

### 🔐 Setting Up Credentials Securely

**Step 1**: Set your actual Msegat credentials using Firebase CLI:

```bash
# Replace with your actual credentials from Msegat dashboard
firebase functions:config:set msegat.username="your_actual_username"
firebase functions:config:set msegat.api_key="your_actual_api_key"
firebase functions:config:set msegat.sender_id="your_actual_sender_id"
firebase functions:config:set msegat.message_template="رمز التحقق: xxxx"
```

**Step 2**: Verify configuration:
```bash
firebase functions:config:get
```

### 🚫 What NOT to Do

- ❌ Never commit actual credentials to Git
- ❌ Never hardcode API keys in source code
- ❌ Never share credentials in documentation or comments
- ❌ Never use production credentials in development/testing

### 🔍 How Credentials Are Used

The functions load credentials at runtime:

```typescript
const MSEGAT_CONFIG = {
  username: functions.config().msegat?.username,
  apiKey: functions.config().msegat?.api_key,
  // ... other config
};

// Validation ensures credentials are available
if (!MSEGAT_CONFIG.username || !MSEGAT_CONFIG.apiKey) {
  functions.logger.error("Missing required Msegat configuration");
}
```

## Additional Security Features

### Firestore Security Rules
- OTP collection is completely inaccessible from client-side
- Users can only access their own data in the users collection
- All other collections are denied by default

### Function Security
- CORS protection enabled
- Input validation for all parameters
- Phone number format validation
- OTP expiry and single-use validation
- Comprehensive error handling without exposing sensitive information

### Logging Security
- Sensitive data (OTPs, API keys) are not logged
- Only necessary information is logged for debugging
- All logs include appropriate context for monitoring

## Environment-Specific Configuration

### Development
```bash
# Use test credentials for development
firebase functions:config:set msegat.username="test_username"
firebase functions:config:set msegat.api_key="test_api_key"
```

### Production
```bash
# Use production credentials
firebase functions:config:set msegat.username="prod_username"
firebase functions:config:set msegat.api_key="prod_api_key"
```

## Monitoring & Alerts

- Monitor function logs for configuration errors
- Set up alerts for failed SMS deliveries
- Monitor for unusual OTP request patterns
- Track authentication success/failure rates

## Credential Rotation

When rotating Msegat credentials:

1. Update credentials in Msegat dashboard
2. Update Firebase Functions configuration:
   ```bash
   firebase functions:config:set msegat.api_key="new_api_key"
   ```
3. Redeploy functions:
   ```bash
   firebase deploy --only functions
   ```
4. Test functionality with new credentials

## Emergency Response

If credentials are compromised:

1. **Immediately** rotate credentials in Msegat dashboard
2. Update Firebase Functions configuration with new credentials
3. Redeploy functions immediately
4. Monitor logs for any unauthorized usage
5. Review access logs and audit trails
