# Flutter Development Workflow with API Keys

## ❓ **Common Questions Answered**

### **Q: Do I need to define API_KEY every time I run the app?**
**A: Yes!** `--dart-define` is a build-time parameter, not a persistent setting.

### **Q: What about iOS builds?**
**A: Same process!** Use `flutter build ios` instead of `flutter build apk`.

## 🚀 **Easy Development Setup**

### Method 1: Create Scripts (Recommended)

**Create these files in your Flutter project root:**

#### `scripts/dev.sh` (Development)
```bash
#!/bin/bash
echo "🚀 Running in DEVELOPMENT mode..."
flutter run --dart-define=API_KEY=your_dev_key_here --dart-define=ENV=development
```

#### `scripts/prod.sh` (Production Testing)
```bash
#!/bin/bash
echo "🚀 Running in PRODUCTION mode..."
flutter run --dart-define=API_KEY=your_prod_key_here --dart-define=ENV=production
```

#### `scripts/build.sh` (Build All Platforms)
```bash
#!/bin/bash

# Configuration
DEV_API_KEY="your_dev_key_here"
PROD_API_KEY="your_prod_key_here"

echo "🏗️ Building Towasl App..."

# Ask for environment
echo "Select environment:"
echo "1) Development"
echo "2) Production"
read -p "Enter choice (1-2): " choice

case $choice in
    1)
        API_KEY=$DEV_API_KEY
        ENV="development"
        echo "📱 Building for DEVELOPMENT..."
        ;;
    2)
        API_KEY=$PROD_API_KEY
        ENV="production"
        echo "📱 Building for PRODUCTION..."
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

# Ask for platform
echo "Select platform:"
echo "1) Android APK"
echo "2) Android App Bundle"
echo "3) iOS"
echo "4) All platforms"
read -p "Enter choice (1-4): " platform

case $platform in
    1)
        flutter build apk --release --dart-define=API_KEY=$API_KEY --dart-define=ENV=$ENV
        echo "✅ Android APK built: build/app/outputs/flutter-apk/app-release.apk"
        ;;
    2)
        flutter build appbundle --release --dart-define=API_KEY=$API_KEY --dart-define=ENV=$ENV
        echo "✅ Android Bundle built: build/app/outputs/bundle/release/app-release.aab"
        ;;
    3)
        flutter build ios --release --dart-define=API_KEY=$API_KEY --dart-define=ENV=$ENV
        echo "✅ iOS built: build/ios/iphoneos/Runner.app"
        echo "💡 Open ios/Runner.xcworkspace in Xcode to archive for App Store"
        ;;
    4)
        echo "🏗️ Building all platforms..."
        flutter build apk --release --dart-define=API_KEY=$API_KEY --dart-define=ENV=$ENV
        flutter build appbundle --release --dart-define=API_KEY=$API_KEY --dart-define=ENV=$ENV
        flutter build ios --release --dart-define=API_KEY=$API_KEY --dart-define=ENV=$ENV
        echo "✅ All platforms built!"
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac
```

**Make scripts executable:**
```bash
chmod +x scripts/*.sh
```

**Usage:**
```bash
# Development
./scripts/dev.sh

# Production testing
./scripts/prod.sh

# Build (interactive)
./scripts/build.sh
```

### Method 2: VS Code Integration

**Create `.vscode/launch.json`:**
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "🚀 Development",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--dart-define=API_KEY=your_dev_key_here",
                "--dart-define=ENV=development"
            ]
        },
        {
            "name": "🏭 Production",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--dart-define=API_KEY=your_prod_key_here",
                "--dart-define=ENV=production"
            ]
        }
    ]
}
```

**Usage in VS Code:**
1. Press `F5` or `Ctrl+Shift+D`
2. Select "🚀 Development" or "🏭 Production"
3. Click the play button

### Method 3: Package.json Style (Alternative)

**Create `scripts.yaml` in project root:**
```yaml
scripts:
  dev: flutter run --dart-define=API_KEY=your_dev_key --dart-define=ENV=development
  prod: flutter run --dart-define=API_KEY=your_prod_key --dart-define=ENV=production
  build-android: flutter build apk --release --dart-define=API_KEY=your_prod_key --dart-define=ENV=production
  build-ios: flutter build ios --release --dart-define=API_KEY=your_prod_key --dart-define=ENV=production
```

**Install script runner:**
```bash
dart pub global activate scripts
```

**Usage:**
```bash
scripts dev      # Run development
scripts prod     # Run production
scripts build-android  # Build Android
scripts build-ios      # Build iOS
```

## 🏗️ **Complete Build Commands**

### Android Builds

```bash
# Development APK
flutter build apk --debug --dart-define=API_KEY=dev_key --dart-define=ENV=development

# Production APK (for testing)
flutter build apk --release --dart-define=API_KEY=prod_key --dart-define=ENV=production

# Production App Bundle (for Google Play Store)
flutter build appbundle --release --dart-define=API_KEY=prod_key --dart-define=ENV=production
```

### iOS Builds

```bash
# Development build
flutter build ios --debug --dart-define=API_KEY=dev_key --dart-define=ENV=development

# Production build
flutter build ios --release --dart-define=API_KEY=prod_key --dart-define=ENV=production

# After iOS build, open Xcode for App Store submission:
open ios/Runner.xcworkspace
```

### Build Output Locations

**Android:**
- APK: `build/app/outputs/flutter-apk/app-release.apk`
- Bundle: `build/app/outputs/bundle/release/app-release.aab`

**iOS:**
- App: `build/ios/iphoneos/Runner.app`
- Archive: Created through Xcode after opening `ios/Runner.xcworkspace`

## 🔧 **IDE Configuration**

### Android Studio
1. Run → Edit Configurations
2. Select Flutter configuration
3. Additional run args: `--dart-define=API_KEY=your_key --dart-define=ENV=development`
4. Create separate configs for dev/prod

### IntelliJ IDEA
Same as Android Studio

### VS Code
Use the `launch.json` configuration shown above

## 💡 **Best Practices**

### 1. Environment-Specific Keys
```bash
# Development (local/staging backend)
DEV_API_KEY="dev_sk_live_..."

# Production (live backend)
PROD_API_KEY="prod_sk_live_..."
```

### 2. Secure Key Storage
```bash
# Store keys in secure files (add to .gitignore)
echo "DEV_API_KEY=your_dev_key" > .env.dev
echo "PROD_API_KEY=your_prod_key" > .env.prod
echo ".env.*" >> .gitignore

# Load in scripts
source .env.dev
flutter run --dart-define=API_KEY=$DEV_API_KEY
```

### 3. Team Development
```bash
# Create template file for team
echo "DEV_API_KEY=ask_team_lead_for_key" > .env.template
echo "PROD_API_KEY=ask_team_lead_for_key" >> .env.template

# Team members copy and fill in actual keys
cp .env.template .env.dev
# Edit .env.dev with actual keys
```

## 🚨 **Important Notes**

1. **API keys are build-time constants** - you must provide them every time
2. **Different keys for different environments** - never use production keys in development
3. **Keys are embedded in the app** - they can be extracted by determined attackers
4. **Use additional security layers** - API keys + App Check + rate limiting
5. **Monitor usage** - watch for unusual API key usage patterns

## 📋 **Quick Reference**

```bash
# Daily development
./scripts/dev.sh

# Test production config
./scripts/prod.sh

# Build for Google Play Store
flutter build appbundle --release --dart-define=API_KEY=prod_key --dart-define=ENV=production

# Build for App Store
flutter build ios --release --dart-define=API_KEY=prod_key --dart-define=ENV=production
open ios/Runner.xcworkspace

# Emergency key rotation
# 1. Generate new key in Firebase
# 2. Update scripts with new key
# 3. Rebuild and redeploy apps
```

---

**Remember**: The `--dart-define` approach is secure and industry-standard, but requires the parameter every time. Use scripts and IDE configurations to make this seamless!
