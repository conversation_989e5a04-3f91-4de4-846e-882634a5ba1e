# Quick Setup Guide - Towasl Backend

## 🚀 Complete Setup in 5 Steps

### Step 1: Install Dependencies
```bash
cd functions
npm install
```

### Step 2: Configure Environment (UPDATED)
```bash
# 1. Generate and configure API key
API_KEY=$(openssl rand -hex 16)
echo "🔑 Your API Key: $API_KEY"
echo "📝 CRITICAL: Save this key for your Flutter app!"

# 2. Set all configuration
firebase functions:config:set msegat.username="YOUR_MSEGAT_USERNAME"
firebase functions:config:set msegat.api_key="YOUR_MSEGAT_API_KEY"
firebase functions:config:set msegat.sender_id="YOUR_SENDER_ID"
firebase functions:config:set msegat.message_template="رمز التحقق: xxxx"
firebase functions:config:set api.key="$API_KEY"
firebase functions:config:set api.require_key="true"

# 3. Verify configuration
firebase functions:config:get

# 4. Save API key securely (recommended)
echo "TOWASL_API_KEY=$API_KEY" >> .env.production
echo ".env.*" >> .gitignore
```

### Step 3: Deploy Functions
```bash
firebase deploy --only functions
```

### Step 4: Test API Security
```bash
# Test without API key (should fail with 401)
curl -X POST https://us-central1-towasl.cloudfunctions.net/sendOtp \
  -H "Content-Type: application/json" \
  -d '{"countryCode": "+966", "mobile": "512345678"}'

# Test with API key (should work - replace $API_KEY with your actual key)
curl -X POST https://us-central1-towasl.cloudfunctions.net/sendOtp \
  -H "Content-Type: application/json" \
  -H "X-API-Key: $API_KEY" \
  -d '{"countryCode": "+966", "mobile": "YOUR_PHONE"}'
```

### Step 5: Update Flutter App

#### A. Update your Flutter code:
```dart
// lib/config/app_config.dart
class AppConfig {
  static const String apiKey = String.fromEnvironment('API_KEY');
  static const String env = String.fromEnvironment('ENV', defaultValue: 'development');

  static bool get isProduction => env == 'production';
  static bool get isApiKeyConfigured => apiKey.isNotEmpty;
}

// lib/services/auth_service.dart
import '../config/app_config.dart';

class AuthService {
  Future<bool> sendOtp({required String countryCode, required String mobile}) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sendOtp'),
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': AppConfig.apiKey, // Secure API key usage
      },
      body: jsonEncode({'countryCode': countryCode, 'mobile': mobile}),
    );
    return response.statusCode == 200;
  }
}
```

#### B. Create development scripts:
```bash
# scripts/dev.sh
#!/bin/bash
flutter run --dart-define=API_KEY=YOUR_GENERATED_API_KEY --dart-define=ENV=development

# scripts/build_android.sh
#!/bin/bash
flutter build apk --release --dart-define=API_KEY=YOUR_GENERATED_API_KEY --dart-define=ENV=production

# scripts/build_ios.sh
#!/bin/bash
flutter build ios --release --dart-define=API_KEY=YOUR_GENERATED_API_KEY --dart-define=ENV=production

# Make executable
chmod +x scripts/*.sh
```

#### C. Usage:
```bash
# Development (you'll run this daily)
./scripts/dev.sh

# Build Android APK
./scripts/build_android.sh

# Build iOS
./scripts/build_ios.sh
```

## 🔐 Security Status After Setup

✅ **What's Now Secure:**
- API key authentication required
- Rate limiting implemented (1 OTP/min per phone, 10/hour per IP)
- Input validation and sanitization
- CORS protection
- Firestore security rules

⚠️ **Still Recommended:**
- Firebase App Check (see FIREBASE_APP_CHECK_SETUP.md)
- Monitoring alerts (see MONITORING_ALERTS_SETUP.md)
- Msegat IP whitelisting
- Flutter secure key storage (see FLUTTER_API_KEY_SECURITY.md)

## 🧪 Testing Checklist

- [ ] Functions deploy successfully
- [ ] API key authentication works (401 without key)
- [ ] OTP sending works with valid API key
- [ ] Rate limiting prevents abuse
- [ ] Flutter app updated with API key
- [ ] Monitoring set up in Firebase Console

## 📚 Additional Documentation

- **API_KEY_SETUP.md** - Detailed API key configuration
- **FLUTTER_API_KEY_SECURITY.md** - Secure Flutter implementation
- **FIREBASE_APP_CHECK_SETUP.md** - Advanced app verification
- **MONITORING_ALERTS_SETUP.md** - Production monitoring
- **SECURITY_RECOMMENDATIONS.md** - Complete security guide

## 🆘 Troubleshooting

### Common Issues:

**"Unauthorized - Invalid or missing API key"**
- Check API key is set: `firebase functions:config:get`
- Verify header name is `X-API-Key`
- Ensure key matches exactly

**"SMS service configuration error"**
- Verify Msegat credentials are set
- Check credentials are correct in Msegat dashboard

**Rate limit errors**
- Wait 1 minute between OTP requests to same number
- Check IP-based limits (10/hour)

### Debug Commands:
```bash
# Check configuration
firebase functions:config:get

# View logs
firebase functions:log

# Test locally
cd functions && npm run serve
```

## 🚨 Important Notes

1. **Save your API key immediately** - you can't retrieve it later
2. **Never commit API keys to Git** - use environment variables
3. **Test thoroughly** before production deployment
4. **Monitor costs** - each OTP costs money
5. **Set up alerts** for unusual activity

---

**Your API is now significantly more secure! 🛡️**

The API key authentication alone blocks most automated abuse. Combine with other security measures for production-grade protection.
