📋 Postman Collection Features:

🔧 Collection Variables:
baseUrl: Set to your Firebase Functions URL (https://us-central1-towasl.cloudfunctions.net)
apiKey: Placeholder for your API key
firebaseIdToken: Placeholder for Firebase ID token (needed for protected endpoints)
testCountryCode & testMobile: Test values for easy testing

📱 API Endpoints Included:

1. Authentication Flow:
Send OTP - POST /sendOtp
Sends 4-digit OTP via SMS
Includes rate limiting info
Sample request/response examples
Verify OTP & Login - POST /verifyOtpAndSignupLogin
Verifies OTP and handles signup/login
Returns Firebase custom token
Includes user data with DUID

2. Account Management:
Delete Account - POST /deleteAccount
Soft delete with Firebase ID token authentication
Signs out user from all devices
Includes proper authorization headers

🚀 How to Use after import:
1. Set Variables:
apiKey: Get your API key with firebase functions:config:get
firebaseIdToken: After successful OTP verification, use the customToken with Firebase Auth to get an ID token

2. Testing Flow:
Use "Send OTP" with a real phone number
Check SMS for OTP code
Use "Verify OTP & Login" with the received OTP
For delete account, first authenticate with Firebase using the custom token

📝 Additional Features:
Detailed descriptions for each endpoint
Sample responses included
Rate limiting information
Security headers properly configured
Error response examples in documentation