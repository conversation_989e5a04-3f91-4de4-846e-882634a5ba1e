import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import axios from "axios";
import { MSEGAT_CONFIG, RATE_LIMITS } from "../config";

// Firestore and Auth references
export const db = admin.firestore();
export const auth = admin.auth();

// Utility function to generate 4-digit OTP
export function generateOTP(): string {
  return Math.floor(1000 + Math.random() * 9000).toString();
}

// Utility function to generate unique 6-character DUID
export async function generateUniqueDUID(): Promise<string> {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let duid: string;
  let isUnique = false;

  while (!isUnique) {
    duid = "";
    for (let i = 0; i < 6; i++) {
      duid += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    // Check if DUID already exists
    const existingUser = await db.collection("users")
      .where("duid", "==", duid)
      .limit(1)
      .get();

    if (existingUser.empty) {
      isUnique = true;
    }
  }

  return duid!;
}

// Phone number validation function
export function isValidPhoneNumber(phoneNumber: string): boolean {
  // Basic validation for international phone numbers
  // Should start with + and contain 10-15 digits
  const phoneRegex = /^\+[1-9]\d{9,14}$/;
  return phoneRegex.test(phoneNumber);
}

// API Key validation function
export function validateApiKey(request: functions.https.Request, apiConfig: { apiKey?: string; requireApiKey: boolean }): boolean {
  // Skip API key validation if not required (for development)
  if (!apiConfig.requireApiKey) {
    return true;
  }

  const providedKey = request.headers["x-api-key"] as string;

  if (!apiConfig.apiKey) {
    functions.logger.error("API key not configured but required");
    return false;
  }

  if (!providedKey) {
    functions.logger.warn("API key missing from request");
    return false;
  }

  if (providedKey !== apiConfig.apiKey) {
    functions.logger.warn("Invalid API key provided");
    return false;
  }

  return true;
}

// Rate limiting helper functions
export async function checkPhoneRateLimit(phoneNumber: string): Promise<boolean> {
  const now = admin.firestore.Timestamp.now();
  const oneMinuteAgo = admin.firestore.Timestamp.fromMillis(now.toMillis() - 60000);

  const recentRequests = await db.collection("rate_limits")
    .where("phoneNumber", "==", phoneNumber)
    .where("type", "==", "otp_request")
    .where("timestamp", ">", oneMinuteAgo)
    .get();

  return recentRequests.size < RATE_LIMITS.OTP_PER_PHONE_PER_MINUTE;
}

export async function checkIPRateLimit(ipAddress: string): Promise<boolean> {
  const now = admin.firestore.Timestamp.now();
  const oneHourAgo = admin.firestore.Timestamp.fromMillis(now.toMillis() - 3600000);

  const recentRequests = await db.collection("rate_limits")
    .where("ipAddress", "==", ipAddress)
    .where("type", "==", "otp_request")
    .where("timestamp", ">", oneHourAgo)
    .get();

  return recentRequests.size < RATE_LIMITS.OTP_PER_IP_PER_HOUR;
}

export async function recordRateLimit(phoneNumber: string, ipAddress: string, type: string): Promise<void> {
  await db.collection("rate_limits").add({
    phoneNumber: phoneNumber,
    ipAddress: ipAddress,
    type: type,
    timestamp: admin.firestore.Timestamp.now(),
  });
}

// Function to send SMS via Msegat API
export async function sendSMS(phoneNumber: string, message: string): Promise<{ success: boolean; data?: any; code?: string }> {
  try {
    const response = await axios.post(MSEGAT_CONFIG.baseUrl, {
      userName: MSEGAT_CONFIG.username,
      apiKey: MSEGAT_CONFIG.apiKey,
      numbers: phoneNumber,
      userSender: MSEGAT_CONFIG.senderId,
      msg: message,
      msgEncoding: "UTF8",
    }, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    functions.logger.info("Msegat API response:", response.data);

    // Check if the response indicates success
    // Msegat typically returns a success code in the response
    const success = response.status === 200 && response.data;
    return {
      success,
      data: response.data,
      code: response.data?.code || response.data?.Code || response.data
    };
  } catch (error) {
    functions.logger.error("Error sending SMS via Msegat:", error);
    return { success: false };
  }
}

// Get client IP address from request
export function getClientIP(request: functions.https.Request): string {
  return (request.headers["x-forwarded-for"] as string)?.split(",")[0]?.trim() ||
         request.connection.remoteAddress ||
         request.socket.remoteAddress ||
         "unknown";
}
